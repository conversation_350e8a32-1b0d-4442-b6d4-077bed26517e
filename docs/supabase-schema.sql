-- ===============================================
-- SUPABASE SCHEMA FOR APPRAISAL TOOL
-- All tables prefixed with appy_
-- ===============================================

-- Create custom types
CREATE TYPE appy_appraisal_status AS ENUM ('draft', 'submitted', 'completed', 'pending');
CREATE TYPE appy_user_role AS ENUM ('super-admin', 'hr-admin', 'manager', 'accountant');
CREATE TYPE appy_compensation_type AS ENUM ('hourly', 'yearly');

-- ===============================================
-- TABLES
-- ===============================================

-- Departments table
CREATE TABLE appy_departments (
  id              uuid primary key default gen_random_uuid(),
  name            text not null unique,
  created_at      timestamptz default now()
);

-- Employees table  
CREATE TABLE appy_employees (
  id              uuid primary key default gen_random_uuid(),
  full_name       text not null,
  compensation    numeric(10,2) not null,
  rate            appy_compensation_type not null,
  department_id   uuid references appy_departments(id),
  manager_id      text, -- Clerk user ID
  active          boolean default true,
  created_at      timestamptz default now()
);

-- Managers table (Clerk users)
CREATE TABLE appy_managers (
  user_id         text primary key, -- Clerk user ID
  full_name       text not null,
  email           text not null,
  department_id   uuid references appy_departments(id),
  active          boolean default true,
  created_at      timestamptz default now()
);

-- User roles table
CREATE TABLE appy_user_roles (
  user_id         text primary key references appy_managers(user_id) on delete cascade,
  role            appy_user_role not null
);

-- Appraisal periods table
CREATE TABLE appy_appraisal_periods (
  id              uuid primary key default gen_random_uuid(),
  name            text not null,
  start_date      date not null,
  end_date        date not null,
  status          text default 'draft' check (status in ('draft', 'active', 'closed')),
  created_at      timestamptz default now()
);

-- Appraisals table
CREATE TABLE appy_appraisals (
  id              uuid primary key default gen_random_uuid(),
  employee_id     uuid not null references appy_employees(id) on delete cascade,
  period_id       uuid not null references appy_appraisal_periods(id) on delete cascade,
  manager_id      text not null references appy_managers(user_id),
  question_1      text,
  question_2      text,
  question_3      text,
  question_4      text,
  question_5      text,
  status          appy_appraisal_status default 'pending',
  submitted_at    timestamptz,
  created_at      timestamptz default now(),
  unique(employee_id, period_id)
);

-- ===============================================
-- INDEXES FOR PERFORMANCE
-- ===============================================

CREATE INDEX idx_appy_employees_manager_id ON appy_employees(manager_id);
CREATE INDEX idx_appy_employees_department_id ON appy_employees(department_id);
CREATE INDEX idx_appy_employees_active ON appy_employees(active);
CREATE INDEX idx_appy_appraisals_employee_id ON appy_appraisals(employee_id);
CREATE INDEX idx_appy_appraisals_manager_id ON appy_appraisals(manager_id);
CREATE INDEX idx_appy_appraisals_period_id ON appy_appraisals(period_id);
CREATE INDEX idx_appy_appraisals_status ON appy_appraisals(status);

-- ===============================================
-- ROW LEVEL SECURITY POLICIES
-- ===============================================

-- Enable RLS on all tables
ALTER TABLE appy_departments ENABLE ROW LEVEL SECURITY;
ALTER TABLE appy_employees ENABLE ROW LEVEL SECURITY;
ALTER TABLE appy_managers ENABLE ROW LEVEL SECURITY;
ALTER TABLE appy_user_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE appy_appraisal_periods ENABLE ROW LEVEL SECURITY;
ALTER TABLE appy_appraisals ENABLE ROW LEVEL SECURITY;

-- Helper function to get user role
CREATE OR REPLACE FUNCTION get_user_role()
RETURNS appy_user_role AS $$
BEGIN
  RETURN (
    SELECT role 
    FROM appy_user_roles 
    WHERE user_id = auth.uid()::text
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user is super admin or hr admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS boolean AS $$
BEGIN
  RETURN get_user_role() IN ('super-admin', 'hr-admin');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ===============================================
-- DEPARTMENTS POLICIES
-- ===============================================

-- Super admins and HR admins can do everything with departments
CREATE POLICY "Admins can manage departments" ON appy_departments
  FOR ALL USING (is_admin());

-- Managers and accountants can read departments
CREATE POLICY "Users can read departments" ON appy_departments
  FOR SELECT USING (
    get_user_role() IN ('manager', 'accountant', 'super-admin', 'hr-admin')
  );

-- ===============================================
-- EMPLOYEES POLICIES
-- ===============================================

-- Super admins and HR admins can manage all employees
CREATE POLICY "Admins can manage employees" ON appy_employees
  FOR ALL USING (is_admin());

-- Managers can read employees assigned to them
CREATE POLICY "Managers can read their employees" ON appy_employees
  FOR SELECT USING (
    get_user_role() = 'manager' AND manager_id = auth.uid()::text
  );

-- Accountants can read all employees for reporting
CREATE POLICY "Accountants can read employees" ON appy_employees
  FOR SELECT USING (get_user_role() = 'accountant');

-- ===============================================
-- MANAGERS POLICIES
-- ===============================================

-- Super admins and HR admins can manage all managers
CREATE POLICY "Admins can manage managers" ON appy_managers
  FOR ALL USING (is_admin());

-- Managers can read their own record
CREATE POLICY "Managers can read own record" ON appy_managers
  FOR SELECT USING (user_id = auth.uid()::text);

-- Accountants can read manager info
CREATE POLICY "Accountants can read managers" ON appy_managers
  FOR SELECT USING (get_user_role() = 'accountant');

-- ===============================================
-- USER ROLES POLICIES
-- ===============================================

-- Super admins can manage all roles
CREATE POLICY "Super admins can manage roles" ON appy_user_roles
  FOR ALL USING (get_user_role() = 'super-admin');

-- HR admins can manage non-super-admin roles
CREATE POLICY "HR admins can manage roles" ON appy_user_roles
  FOR ALL USING (
    get_user_role() = 'hr-admin' AND 
    (role != 'super-admin' OR user_id = auth.uid()::text)
  );

-- Users can read their own role
CREATE POLICY "Users can read own role" ON appy_user_roles
  FOR SELECT USING (user_id = auth.uid()::text);

-- ===============================================
-- APPRAISAL PERIODS POLICIES
-- ===============================================

-- Super admins and HR admins can manage periods
CREATE POLICY "Admins can manage periods" ON appy_appraisal_periods
  FOR ALL USING (is_admin());

-- All authenticated users can read periods
CREATE POLICY "Users can read periods" ON appy_appraisal_periods
  FOR SELECT USING (auth.uid() IS NOT NULL);

-- ===============================================
-- APPRAISALS POLICIES
-- ===============================================

-- Super admins and HR admins can manage all appraisals
CREATE POLICY "Admins can manage appraisals" ON appy_appraisals
  FOR ALL USING (is_admin());

-- Managers can manage appraisals for their employees
CREATE POLICY "Managers can manage their appraisals" ON appy_appraisals
  FOR ALL USING (
    get_user_role() = 'manager' AND manager_id = auth.uid()::text
  );

-- Accountants can read all appraisals for reporting
CREATE POLICY "Accountants can read appraisals" ON appy_appraisals
  FOR SELECT USING (get_user_role() = 'accountant');

-- ===============================================
-- SEED DATA
-- ===============================================

-- Create sample departments
INSERT INTO appy_departments (name) VALUES 
  ('Engineering'),
  ('Marketing'), 
  ('Sales'),
  ('HR'),
  ('Finance');

-- Create sample appraisal period
INSERT INTO appy_appraisal_periods (name, start_date, end_date, status) VALUES
  ('Q4 2024 Performance Review', '2024-10-01', '2024-12-31', 'active'),
  ('Q1 2025 Performance Review', '2025-01-01', '2025-03-31', 'draft');

-- Note: Managers, employees, and appraisals should be created through the application
-- with real Clerk user IDs

-- ===============================================
-- FUNCTIONS FOR APPLICATION USE
-- ===============================================

-- Function to get employees with department and manager info
CREATE OR REPLACE FUNCTION get_employees_with_details()
RETURNS TABLE (
  id uuid,
  full_name text,
  compensation numeric,
  rate appy_compensation_type,
  active boolean,
  created_at timestamptz,
  department_id uuid,
  department_name text,
  manager_id text,
  manager_name text
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    e.id,
    e.full_name,
    e.compensation,
    e.rate,
    e.active,
    e.created_at,
    d.id as department_id,
    d.name as department_name,
    m.user_id as manager_id,
    m.full_name as manager_name
  FROM appy_employees e
  LEFT JOIN appy_departments d ON e.department_id = d.id
  LEFT JOIN appy_managers m ON e.manager_id = m.user_id
  WHERE e.active = true
  ORDER BY e.full_name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get manager appraisals overview
CREATE OR REPLACE FUNCTION get_manager_appraisals(manager_user_id text)
RETURNS TABLE (
  employee_id uuid,
  employee_name text,
  department_name text,
  compensation numeric,
  rate appy_compensation_type,
  appraisal_status appy_appraisal_status,
  period_name text
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    e.id as employee_id,
    e.full_name as employee_name,
    d.name as department_name,
    e.compensation,
    e.rate,
    COALESCE(a.status, 'pending'::appy_appraisal_status) as appraisal_status,
    p.name as period_name
  FROM appy_employees e
  LEFT JOIN appy_departments d ON e.department_id = d.id
  LEFT JOIN appy_appraisals a ON e.id = a.employee_id
  LEFT JOIN appy_appraisal_periods p ON a.period_id = p.id
  WHERE e.manager_id = manager_user_id
    AND e.active = true
    AND (p.status = 'active' OR p.status IS NULL)
  ORDER BY e.full_name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;