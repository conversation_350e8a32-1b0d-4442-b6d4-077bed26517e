"use client"

import * as React from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { AlertTriangle, Loader } from "lucide-react"
import type { Employee, Department, Manager } from "@/lib/types"
import { employeeFormSchema, type Employee as EmployeeSchema } from "@/lib/schemas"
import { saveEmployeeAction } from "@/lib/actions"
import { useTransition } from "react"

interface EmployeeFormDialogProps {
  isOpen: boolean
  onClose: () => void
  employee: Employee | null
  departments: Department[]
  managers: Manager[]
}

type EmployeeFormData = Omit<EmployeeSchema, 'id'>

export function EmployeeFormDialog({ isOpen, onClose, employee, departments, managers }: EmployeeFormDialogProps) {
  const [isPending, startTransition] = useTransition()
  const [submitError, setSubmitError] = React.useState<string | null>(null)

  const form = useForm<EmployeeFormData>({
    resolver: zodResolver(employeeFormSchema),
    defaultValues: {
      fullName: "",
      departmentId: "",
      managerId: null,
      compensation: "monthly",
      rate: 0,
      active: true,
    },
  })

  // Reset form when dialog opens/closes or employee changes
  React.useEffect(() => {
    if (isOpen) {
      setSubmitError(null)
      if (employee) {
        form.reset({
          fullName: employee.fullName,
          departmentId: employee.departmentId,
          managerId: employee.managerId,
          compensation: employee.compensation,
          rate: employee.rate,
          active: employee.active,
        })
      } else {
        form.reset({
          fullName: "",
          departmentId: "",
          managerId: null,
          compensation: "monthly",
          rate: 0,
          active: true,
        })
      }
    }
  }, [employee, isOpen, form])

  const onSubmit = async (data: EmployeeFormData) => {
    setSubmitError(null)

    startTransition(async () => {
      try {
        // Create FormData for server action
        const formData = new FormData()
        if (employee?.id) {
          formData.append('id', employee.id)
        }
        formData.append('fullName', data.fullName)
        formData.append('departmentId', data.departmentId)
        formData.append('managerId', data.managerId || '')
        formData.append('compensation', data.compensation)
        formData.append('rate', data.rate.toString())
        formData.append('active', data.active.toString())

        const result = await saveEmployeeAction(formData)

        if (result.success) {
          onClose()
        } else {
          setSubmitError('error' in result ? result.error : 'Failed to save employee')
        }
      } catch (error) {
        console.error('Form submission error:', error)
        setSubmitError('An unexpected error occurred. Please try again.')
      }
    })
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{employee ? "Edit Employee" : "Add New Employee"}</DialogTitle>
          <DialogDescription>
            {employee ? "Make changes to the employee profile." : "Add a new employee to the system."}
          </DialogDescription>
        </DialogHeader>

        {submitError && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{submitError}</AlertDescription>
          </Alert>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="fullName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Full Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter employee's full name"
                      {...field}
                      disabled={isPending}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="departmentId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Department</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                    disabled={isPending}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a department" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {departments.map((dept) => (
                        <SelectItem key={dept.id} value={dept.id}>
                          {dept.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="managerId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Manager (Optional)</FormLabel>
                  <Select
                    onValueChange={(value) => field.onChange(value || null)}
                    value={field.value || ""}
                    disabled={isPending}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Assign a manager" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="">No manager assigned</SelectItem>
                      {managers.map((manager) => (
                        <SelectItem key={manager.id} value={manager.id}>
                          {manager.fullName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="compensation"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Compensation Type</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      value={field.value}
                      className="flex space-x-4"
                      disabled={isPending}
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="monthly" id="monthly" />
                        <Label htmlFor="monthly">Monthly Salary</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="hourly" id="hourly" />
                        <Label htmlFor="hourly">Hourly Rate</Label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="rate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Rate (USD {form.watch('compensation') === 'hourly' ? 'per hour' : 'per month'})
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Enter rate"
                      {...field}
                      onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                      disabled={isPending}
                      min="0"
                      step="0.01"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className="gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isPending}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isPending || !form.formState.isValid}
              >
                {isPending ? (
                  <>
                    <Loader className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  employee ? "Update Employee" : "Create Employee"
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
