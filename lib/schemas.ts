import { z } from 'zod'

// Base validation helpers
const sanitizeString = (str: string) => str.trim().replace(/[<>]/g, '')
const nonEmptyString = z.string().min(1, 'This field is required').transform(sanitizeString)

// User role validation
export const userRoleSchema = z.enum(['super-admin', 'hr-admin', 'admin', 'manager', 'accountant'])

// Department validation
export const departmentSchema = z.object({
  id: z.string().uuid().optional(),
  name: z.string()
    .min(2, 'Department name must be at least 2 characters')
    .max(50, 'Department name must be less than 50 characters')
    .transform(sanitizeString)
    .refine(name => !/^\s*$/.test(name), 'Department name cannot be empty or whitespace'),
})

export const departmentFormSchema = departmentSchema.omit({ id: true })

// Manager validation
export const managerSchema = z.object({
  id: z.string().uuid('Invalid manager ID'),
  fullName: z.string()
    .min(2, 'Manager name must be at least 2 characters')
    .max(100, 'Manager name must be less than 100 characters')
    .transform(sanitizeString),
})

// Employee validation
export const employeeSchema = z.object({
  id: z.string().uuid().optional(),
  fullName: z.string()
    .min(2, 'Full name must be at least 2 characters')
    .max(100, 'Full name must be less than 100 characters')
    .transform(sanitizeString)
    .refine(name => /^[a-zA-Z\s'-]+$/.test(name), 'Name can only contain letters, spaces, hyphens, and apostrophes'),
  compensation: z.enum(['hourly', 'monthly'], {
    errorMap: () => ({ message: 'Compensation must be either hourly or monthly' })
  }),
  rate: z.number()
    .positive('Rate must be positive')
    .max(1000000, 'Rate cannot exceed $1,000,000')
    .refine(rate => Number.isFinite(rate), 'Rate must be a valid number'),
  departmentId: z.string().uuid('Invalid department selected'),
  managerId: z.string().uuid('Invalid manager selected').nullable(),
  active: z.boolean().default(true),
})

export const employeeFormSchema = employeeSchema.omit({ id: true }).extend({
  active: z.boolean(), // Make active field required for form
})

// Appraisal Period validation
const appraisalPeriodBaseObject = z.object({
  periodStart: z.string()
    .datetime('Invalid start date format')
    .or(z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format')),
  periodEnd: z.string()
    .datetime('Invalid end date format')
    .or(z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format')),
  closed: z.boolean().default(false),
})

const appraisalPeriodWithValidation = appraisalPeriodBaseObject.refine(data => {
  const start = new Date(data.periodStart)
  const end = new Date(data.periodEnd)
  return end > start
}, {
  message: "End date must be after start date",
  path: ["periodEnd"],
}).refine(data => {
  const start = new Date(data.periodStart)
  const end = new Date(data.periodEnd)
  const diffTime = Math.abs(end.getTime() - start.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays <= 365
}, {
  message: "Period cannot be longer than 365 days",
  path: ["periodEnd"],
})

export const appraisalPeriodSchema = appraisalPeriodBaseObject.extend({
  id: z.string().uuid().optional(),
}).refine(data => {
  const start = new Date(data.periodStart)
  const end = new Date(data.periodEnd)
  return end > start
}, {
  message: "End date must be after start date",
  path: ["periodEnd"],
}).refine(data => {
  const start = new Date(data.periodStart)
  const end = new Date(data.periodEnd)
  const diffTime = Math.abs(end.getTime() - start.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays <= 365
}, {
  message: "Period cannot be longer than 365 days",
  path: ["periodEnd"],
})

export const appraisalPeriodFormSchema = appraisalPeriodBaseObject.extend({
  closed: z.boolean(), // Make closed field required for form
}).refine(data => {
  const start = new Date(data.periodStart)
  const end = new Date(data.periodEnd)
  return end > start
}, {
  message: "End date must be after start date",
  path: ["periodEnd"],
}).refine(data => {
  const start = new Date(data.periodStart)
  const end = new Date(data.periodEnd)
  const diffTime = Math.abs(end.getTime() - start.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays <= 365
}, {
  message: "Period cannot be longer than 365 days",
  path: ["periodEnd"],
})

// Appraisal validation
export const appraisalSchema = z.object({
  id: z.string().uuid().optional(),
  periodId: z.string().uuid('Invalid period'),
  employeeId: z.string().uuid('Invalid employee'),
  managerId: z.string().uuid('Invalid manager'),
  q1: z.enum(['below-expectations', 'meets-expectations', 'exceeds-expectations']).nullable(),
  q2: z.boolean(),
  q3: z.string()
    .max(500, 'Response must be less than 500 characters')
    .transform(sanitizeString),
  q4: z.string()
    .max(1000, 'Response must be less than 1000 characters')
    .transform(sanitizeString),
  q5: z.string()
    .max(1000, 'Response must be less than 1000 characters')
    .transform(sanitizeString),
  status: z.enum(['draft', 'submitted']).default('draft'),
})

// Appraisal submission validation (requires all fields)
export const appraisalSubmissionSchema = appraisalSchema.extend({
  q1: z.enum(['below-expectations', 'meets-expectations', 'exceeds-expectations'], {
    errorMap: () => ({ message: 'Overall performance rating is required' })
  }),
  q3: z.string()
    .min(10, 'Primary project/focus area must be at least 10 characters')
    .max(500, 'Response must be less than 500 characters')
    .transform(sanitizeString),
  q4: z.string()
    .min(20, 'Achievements and areas for improvement must be at least 20 characters')
    .max(1000, 'Response must be less than 1000 characters')
    .transform(sanitizeString),
  q5: z.string()
    .min(10, 'Next month goals must be at least 10 characters')
    .max(1000, 'Response must be less than 1000 characters')
    .transform(sanitizeString),
  status: z.literal('submitted'),
})

export const appraisalFormSchema = appraisalSchema.omit({ 
  id: true, 
  periodId: true, 
  employeeId: true, 
  managerId: true, 
  status: true 
})

// Search and filter schemas
export const searchSchema = z.object({
  query: z.string()
    .max(100, 'Search query too long')
    .transform(sanitizeString)
    .optional(),
  department: z.string().uuid().optional(),
  status: z.enum(['draft', 'submitted', 'not-started']).optional(),
  sortBy: z.enum(['name', 'department', 'status', 'submittedAt']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
})

// Pagination schema
export const paginationSchema = z.object({
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(10),
})

// Export validation functions
export const validateDepartment = (data: unknown) => departmentSchema.parse(data)
export const validateEmployee = (data: unknown) => employeeSchema.parse(data)
export const validateAppraisal = (data: unknown) => appraisalSchema.parse(data)
export const validateAppraisalSubmission = (data: unknown) => appraisalSubmissionSchema.parse(data)
export const validatePeriod = (data: unknown) => appraisalPeriodSchema.parse(data)

// Type exports
export type Department = z.infer<typeof departmentSchema>
export type Employee = z.infer<typeof employeeSchema>
export type AppraisalPeriod = z.infer<typeof appraisalPeriodSchema>
export type Appraisal = z.infer<typeof appraisalSchema>
export type AppraisalSubmission = z.infer<typeof appraisalSubmissionSchema>
export type UserRole = z.infer<typeof userRoleSchema>
export type SearchParams = z.infer<typeof searchSchema>
export type PaginationParams = z.infer<typeof paginationSchema>

console.log('📋 Validation schemas loaded successfully')
