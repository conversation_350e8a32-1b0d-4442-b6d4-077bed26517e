import { supabaseAdminQuery, supabaseAdmin } from './supabase'
import type { Database } from './supabase'

// Type aliases for better readability
type Department = Database['public']['Tables']['appy_departments']['Row']
type Employee = Database['public']['Tables']['appy_employees']['Row']
type Manager = Database['public']['Tables']['appy_managers']['Row']
type AppraisalPeriod = Database['public']['Tables']['appy_appraisal_periods']['Row']
type Appraisal = Database['public']['Tables']['appy_appraisals']['Row']

// Extended types with joined data
export interface EmployeeWithDepartment extends Employee {
  department_name: string
  manager_name: string | null
}

export interface AppraisalWithDetails extends Appraisal {
  employee_name: string
  manager_name: string
  department_name: string
}

// Query result type
interface QueryResult<T> {
  rows: T[]
  rowCount: number
}

// Helper function to format Supabase response to match PostgreSQL format
function formatSupabaseResponse<T>(data: T[] | null, error: any): QueryResult<T> {
  if (error) {
    console.error('Supabase query error:', error)
    throw new Error(error.message || 'Database query failed')
  }
  
  return {
    rows: data || [],
    rowCount: data?.length || 0
  }
}

// Execute a query with logging (for compatibility)
export async function query<T = any>(
  description: string,
  queryFn: () => Promise<{ data: T[] | null; error: any }>
): Promise<QueryResult<T>> {
  const start = Date.now()
  
  try {
    const { data, error } = await queryFn()
    const duration = Date.now() - start
    
    if (process.env.NODE_ENV === 'development') {
      console.log('Executed query', { 
        description,
        duration: `${duration}ms`,
        rows: data?.length || 0
      })
    }
    
    return formatSupabaseResponse(data, error)
  } catch (error) {
    console.error('Database query error:', {
      description,
      error: error instanceof Error ? error.message : error
    })
    throw error
  }
}

// Health check function
export async function healthCheck(): Promise<boolean> {
  try {
    const { data, error } = await supabaseAdminQuery.departments().select('id').limit(1)
    return !error && data !== null
  } catch (error) {
    console.error('Database health check failed:', error)
    return false
  }
}

// Database-specific query builders
export const db = {
  // Departments
  async getDepartments(): Promise<Department[]> {
    const { data, error } = await supabaseAdminQuery.departments()
      .select('id, name, created_at')
      .order('name')
    
    if (error) {
      console.error('Failed to fetch departments:', error)
      throw new Error(error.message)
    }
    
    return data || []
  },

  async createDepartment(name: string): Promise<Department> {
    const { data, error } = await supabaseAdminQuery.departments()
      .insert({ name })
      .select()
      .single()
    
    if (error) {
      console.error('Failed to create department:', error)
      throw new Error(error.message)
    }
    
    return data
  },

  async updateDepartment(id: string, name: string): Promise<Department> {
    const { data, error } = await supabaseAdminQuery.departments()
      .update({ name })
      .eq('id', id)
      .select()
      .single()
    
    if (error) {
      console.error('Failed to update department:', error)
      throw new Error(error.message)
    }
    
    return data
  },

  async deleteDepartment(id: string): Promise<void> {
    const { error } = await supabaseAdminQuery.departments()
      .delete()
      .eq('id', id)
    
    if (error) {
      console.error('Failed to delete department:', error)
      throw new Error(error.message)
    }
  },

  // Employees
  async getEmployees(): Promise<EmployeeWithDepartment[]> {
    // Fetch employees with departments only (no manager join due to missing FK constraint)
    const { data: employees, error: employeesError } = await supabaseAdminQuery.employees()
      .select(`
        *,
        appy_departments:department_id (
          id,
          name
        )
      `)
      .eq('active', true)
      .order('full_name')
    
    if (employeesError) {
      console.error('Failed to fetch employees:', employeesError)
      throw new Error(employeesError.message)
    }

    // Fetch all managers separately
    const { data: managers, error: managersError } = await supabaseAdminQuery.managers()
      .select('user_id, full_name')
      .eq('active', true)
    
    if (managersError) {
      console.error('Failed to fetch managers:', managersError)
      throw new Error(managersError.message)
    }

    // Create a lookup map for managers
    const managerMap = new Map(
      (managers || []).map(manager => [manager.user_id, manager.full_name])
    )
    
    // Transform the data to match expected format
    return (employees || []).map(employee => ({
      ...employee,
      department_name: employee.appy_departments?.name || '',
      manager_name: employee.manager_id ? managerMap.get(employee.manager_id) || null : null
    }))
  },

  async getEmployeeById(id: string): Promise<EmployeeWithDepartment | null> {
    // Fetch employee with department only (no manager join due to missing FK constraint)
    const { data: employee, error: employeeError } = await supabaseAdminQuery.employees()
      .select(`
        *,
        appy_departments:department_id (
          id,
          name
        )
      `)
      .eq('id', id)
      .single()
    
    if (employeeError) {
      if (employeeError.code === 'PGRST116') {
        return null // Not found
      }
      console.error('Failed to fetch employee:', employeeError)
      throw new Error(employeeError.message)
    }

    // Fetch manager separately if employee has one
    let managerName = null
    if (employee.manager_id) {
      const { data: manager, error: managerError } = await supabaseAdminQuery.managers()
        .select('full_name')
        .eq('user_id', employee.manager_id)
        .eq('active', true)
        .single()
      
      if (!managerError && manager) {
        managerName = manager.full_name
      }
    }
    
    return {
      ...employee,
      department_name: employee.appy_departments?.name || '',
      manager_name: managerName
    }
  },

  async createEmployee(employeeData: {
    fullName: string
    compensation: number
    rate: 'hourly' | 'monthly'
    departmentId: string
    managerId?: string
  }): Promise<Employee> {
    const { data, error } = await supabaseAdminQuery.employees()
      .insert({
        full_name: employeeData.fullName,
        compensation: employeeData.compensation,
        rate: employeeData.rate,
        department_id: employeeData.departmentId,
        manager_id: employeeData.managerId || null
      })
      .select()
      .single()
    
    if (error) {
      console.error('Failed to create employee:', error)
      throw new Error(error.message)
    }
    
    return data
  },

  async updateEmployee(id: string, employeeData: {
    fullName?: string
    compensation?: number
    rate?: 'hourly' | 'monthly'
    departmentId?: string
    managerId?: string
    active?: boolean
  }): Promise<Employee> {
    const updateData: any = {}
    
    if (employeeData.fullName !== undefined) updateData.full_name = employeeData.fullName
    if (employeeData.compensation !== undefined) updateData.compensation = employeeData.compensation
    if (employeeData.rate !== undefined) updateData.rate = employeeData.rate
    if (employeeData.departmentId !== undefined) updateData.department_id = employeeData.departmentId
    if (employeeData.managerId !== undefined) updateData.manager_id = employeeData.managerId
    if (employeeData.active !== undefined) updateData.active = employeeData.active
    
    if (Object.keys(updateData).length === 0) {
      throw new Error('No fields to update')
    }
    
    const { data, error } = await supabaseAdminQuery.employees()
      .update(updateData)
      .eq('id', id)
      .select()
      .single()
    
    if (error) {
      console.error('Failed to update employee:', error)
      throw new Error(error.message)
    }
    
    return data
  },

  async softDeleteEmployee(id: string): Promise<void> {
    const { error } = await supabaseAdminQuery.employees()
      .update({ active: false })
      .eq('id', id)
    
    if (error) {
      console.error('Failed to soft delete employee:', error)
      throw new Error(error.message)
    }
  },

  // Managers
  async getManagers(): Promise<Manager[]> {
    const { data, error } = await supabaseAdminQuery.managers()
      .select('user_id, full_name, email, department_id, active, created_at')
      .eq('active', true)
      .order('full_name')
    
    if (error) {
      console.error('Failed to fetch managers:', error)
      throw new Error(error.message)
    }
    
    return data || []
  },

  async createManager(userId: string, fullName: string, email: string, departmentId?: string): Promise<Manager> {
    const { data, error } = await supabaseAdminQuery.managers()
      .insert({
        user_id: userId,
        full_name: fullName,
        email: email,
        department_id: departmentId || null
      })
      .select()
      .single()
    
    if (error) {
      console.error('Failed to create manager:', error)
      throw new Error(error.message)
    }
    
    return data
  },

  // Appraisal Periods
  async getAppraisalPeriods(): Promise<AppraisalPeriod[]> {
    const { data, error } = await supabaseAdminQuery.appraisalPeriods()
      .select('id, name, start_date, end_date, status, created_at')
      .order('start_date', { ascending: false })
    
    if (error) {
      console.error('Failed to fetch appraisal periods:', error)
      throw new Error(error.message)
    }
    
    return data || []
  },

  async createAppraisalPeriod(periodData: {
    name: string
    startDate: string
    endDate: string
    status?: string
  }): Promise<AppraisalPeriod> {
    const { data, error } = await supabaseAdminQuery.appraisalPeriods()
      .insert({
        name: periodData.name,
        start_date: periodData.startDate,
        end_date: periodData.endDate,
        status: periodData.status || 'draft'
      })
      .select()
      .single()
    
    if (error) {
      console.error('Failed to create appraisal period:', error)
      throw new Error(error.message)
    }
    
    return data
  },

  async updateAppraisalPeriod(id: string, periodData: {
    name?: string
    startDate?: string
    endDate?: string
    status?: string
  }): Promise<AppraisalPeriod> {
    const updateData: any = {}
    
    if (periodData.name !== undefined) updateData.name = periodData.name
    if (periodData.startDate !== undefined) updateData.start_date = periodData.startDate
    if (periodData.endDate !== undefined) updateData.end_date = periodData.endDate
    if (periodData.status !== undefined) updateData.status = periodData.status
    
    if (Object.keys(updateData).length === 0) {
      throw new Error('No fields to update')
    }
    
    const { data, error } = await supabaseAdminQuery.appraisalPeriods()
      .update(updateData)
      .eq('id', id)
      .select()
      .single()
    
    if (error) {
      console.error('Failed to update appraisal period:', error)
      throw new Error(error.message)
    }
    
    return data
  },

  // Appraisals
  async getAppraisalByEmployeeId(employeeId: string, periodId?: string): Promise<Appraisal | null> {
    let query = supabaseAdminQuery.appraisals()
      .select('*')
      .eq('employee_id', employeeId)
      .order('created_at', { ascending: false })
    
    if (periodId) {
      query = query.eq('period_id', periodId)
    }
    
    const { data, error } = await query.limit(1).single()
    
    if (error) {
      if (error.code === 'PGRST116') {
        return null // Not found
      }
      console.error('Failed to fetch appraisal:', error)
      throw new Error(error.message)
    }
    
    return data
  },

  async saveAppraisalDraft(appraisalData: {
    periodId: string
    employeeId: string
    managerId: string
    question1?: string | null
    question2?: string | null
    question3?: string | null
    question4?: string | null
    question5?: string | null
  }): Promise<Appraisal> {
    const { data, error } = await supabaseAdminQuery.appraisals()
      .upsert({
        period_id: appraisalData.periodId,
        employee_id: appraisalData.employeeId,
        manager_id: appraisalData.managerId,
        question_1: appraisalData.question1 || null,
        question_2: appraisalData.question2 || null,
        question_3: appraisalData.question3 || null,
        question_4: appraisalData.question4 || null,
        question_5: appraisalData.question5 || null,
        status: 'pending'
      }, {
        onConflict: 'period_id,employee_id'
      })
      .select()
      .single()
    
    if (error) {
      console.error('Failed to save appraisal draft:', error)
      throw new Error(error.message)
    }
    
    return data
  },

  async submitAppraisal(id: string): Promise<Appraisal> {
    const { data, error } = await supabaseAdminQuery.appraisals()
      .update({
        status: 'submitted',
        submitted_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()
    
    if (error) {
      console.error('Failed to submit appraisal:', error)
      throw new Error(error.message)
    }
    
    return data
  },

  // Performance Statistics
  async getPerformanceStatsByPeriod(periodId: string): Promise<Appraisal[]> {
    const { data, error } = await supabaseAdminQuery.appraisals()
      .select('*')
      .eq('period_id', periodId)
      .order('created_at', { ascending: false })
    
    if (error) {
      console.error('Failed to fetch performance statistics:', error)
      throw new Error(error.message)
    }
    
    return data || []
  },

  async getAllAppraisalsForManager(managerId: string, periodId?: string): Promise<Appraisal[]> {
    let query = supabaseAdminQuery.appraisals()
      .select('*')
      .eq('manager_id', managerId)
      .order('created_at', { ascending: false })
    
    if (periodId) {
      query = query.eq('period_id', periodId)
    }
    
    const { data, error } = await query
    
    if (error) {
      console.error('Failed to fetch manager appraisals:', error)
      throw new Error(error.message)
    }
    
    return data || []
  },

  async getAppraisalsWithEmployeeData(periodId?: string): Promise<AppraisalWithDetails[]> {
    let query = supabaseAdminQuery.appraisals()
      .select(`
        *,
        appy_employees!inner (
          id,
          full_name,
          compensation,
          rate,
          appy_departments!inner (
            name
          )
        ),
        appy_managers!inner (
          full_name
        )
      `)
      .order('created_at', { ascending: false })
    
    if (periodId) {
      query = query.eq('period_id', periodId)
    }
    
    const { data, error } = await query
    
    if (error) {
      console.error('Failed to fetch appraisals with employee data:', error)
      throw new Error(error.message)
    }
    
    return (data || []).map(appraisal => ({
      ...appraisal,
      employee_name: appraisal.appy_employees?.full_name || '',
      manager_name: appraisal.appy_managers?.full_name || '',
      department_name: appraisal.appy_employees?.appy_departments?.name || ''
    }))
  }
}

console.log('📊 Database module loaded successfully')