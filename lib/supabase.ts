import { createClient } from '@supabase/supabase-js'

// Environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// Validate environment variables
if (!supabaseUrl) {
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_URL environment variable')
}
if (!supabaseAnonKey) {
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable')
}
if (!supabaseServiceRoleKey) {
  throw new Error('Missing SUPABASE_SERVICE_ROLE_KEY environment variable')
}

// Database types (based on the schema)
export interface Database {
  public: {
    Tables: {
      appy_departments: {
        Row: {
          id: string
          name: string
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          created_at?: string
        }
      }
      appy_employees: {
        Row: {
          id: string
          full_name: string
          compensation: number
          rate: 'hourly' | 'monthly'
          department_id: string | null
          manager_id: string | null
          active: boolean
          created_at: string
        }
        Insert: {
          id?: string
          full_name: string
          compensation: number
          rate: 'hourly' | 'monthly'
          department_id?: string | null
          manager_id?: string | null
          active?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          full_name?: string
          compensation?: number
          rate?: 'hourly' | 'monthly'
          department_id?: string | null
          manager_id?: string | null
          active?: boolean
          created_at?: string
        }
      }
      appy_managers: {
        Row: {
          user_id: string
          full_name: string
          email: string
          department_id: string | null
          active: boolean
          created_at: string
        }
        Insert: {
          user_id: string
          full_name: string
          email: string
          department_id?: string | null
          active?: boolean
          created_at?: string
        }
        Update: {
          user_id?: string
          full_name?: string
          email?: string
          department_id?: string | null
          active?: boolean
          created_at?: string
        }
      }
      appy_user_roles: {
        Row: {
          user_id: string
          role: 'super-admin' | 'hr-admin' | 'manager' | 'accountant'
        }
        Insert: {
          user_id: string
          role: 'super-admin' | 'hr-admin' | 'manager' | 'accountant'
        }
        Update: {
          user_id?: string
          role?: 'super-admin' | 'hr-admin' | 'manager' | 'accountant'
        }
      }
      appy_appraisal_periods: {
        Row: {
          id: string
          name: string
          start_date: string
          end_date: string
          status: string
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          start_date: string
          end_date: string
          status?: string
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          start_date?: string
          end_date?: string
          status?: string
          created_at?: string
        }
      }
      appy_appraisals: {
        Row: {
          id: string
          employee_id: string
          period_id: string
          manager_id: string
          question_1: string | null
          question_2: string | null
          question_3: string | null
          question_4: string | null
          question_5: string | null
          status: 'pending' | 'submitted' | 'approved'
          submitted_at: string | null
          created_at: string
        }
        Insert: {
          id?: string
          employee_id: string
          period_id: string
          manager_id: string
          question_1?: string | null
          question_2?: string | null
          question_3?: string | null
          question_4?: string | null
          question_5?: string | null
          status?: 'pending' | 'submitted' | 'approved'
          submitted_at?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          employee_id?: string
          period_id?: string
          manager_id?: string
          question_1?: string | null
          question_2?: string | null
          question_3?: string | null
          question_4?: string | null
          question_5?: string | null
          status?: 'pending' | 'submitted' | 'approved'
          submitted_at?: string | null
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      appy_appraisal_status: 'pending' | 'submitted' | 'approved'
      appy_user_role: 'super-admin' | 'hr-admin' | 'manager' | 'accountant'
      appy_compensation_rate: 'hourly' | 'monthly'
    }
  }
}

// Create client instances
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey)
export const supabaseAdmin = createClient<Database>(supabaseUrl, supabaseServiceRoleKey)

// Helper function to get the appropriate client
export function getSupabaseClient(useServiceRole = false) {
  return useServiceRole ? supabaseAdmin : supabase
}

// Database query helpers
export const supabaseQuery = {
  // Departments
  departments: () => supabase.from('appy_departments'),
  
  // Employees  
  employees: () => supabase.from('appy_employees'),
  
  // Managers
  managers: () => supabase.from('appy_managers'),
  
  // User roles
  userRoles: () => supabase.from('appy_user_roles'),
  
  // Appraisal periods
  appraisalPeriods: () => supabase.from('appy_appraisal_periods'),
  
  // Appraisals
  appraisals: () => supabase.from('appy_appraisals'),
}

// Admin query helpers (for server-side operations)
export const supabaseAdminQuery = {
  // Departments
  departments: () => supabaseAdmin.from('appy_departments'),
  
  // Employees  
  employees: () => supabaseAdmin.from('appy_employees'),
  
  // Managers
  managers: () => supabaseAdmin.from('appy_managers'),
  
  // User roles
  userRoles: () => supabaseAdmin.from('appy_user_roles'),
  
  // Appraisal periods
  appraisalPeriods: () => supabaseAdmin.from('appy_appraisal_periods'),
  
  // Appraisals
  appraisals: () => supabaseAdmin.from('appy_appraisals'),
}

// Types for convenience
export type Department = Database['public']['Tables']['appy_departments']['Row']
export type Employee = Database['public']['Tables']['appy_employees']['Row']
export type Manager = Database['public']['Tables']['appy_managers']['Row']
export type UserRole = Database['public']['Tables']['appy_user_roles']['Row']
export type AppraisalPeriod = Database['public']['Tables']['appy_appraisal_periods']['Row']
export type Appraisal = Database['public']['Tables']['appy_appraisals']['Row']

console.log('📊 Supabase clients initialized successfully')