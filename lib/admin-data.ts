import { supabase } from './supabase'

export interface AdminProfile {
  id: string
  fullName: string
  email: string
  role: 'super-admin' | 'admin'
  supervisorId?: string
  supervisorName?: string
  permissions: Record<string, any>
  createdAt: string
  updatedAt: string
}

export interface AdminPermissions {
  canManageUsers: boolean
  canViewReports: boolean
  canExportData: boolean
  canManageSettings: boolean
  canViewAllAdmins: boolean
  canEditAdmins: boolean
}

// Mock data for the three admins
const mockAdmins: AdminProfile[] = [
  {
    id: 'bob_clerk_user_id_placeholder',
    fullName: '<PERSON>azneh',
    email: '<EMAIL>',
    role: 'super-admin',
    supervisorId: undefined,
    supervisorName: undefined,
    permissions: {
      canManageUsers: true,
      canViewReports: true,
      canExportData: true,
      canManageSettings: true,
      canViewAllAdmins: true,
      canEditAdmins: true
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'tarek_clerk_user_id_placeholder',
    fullName: 'Tarek H',
    email: '<EMAIL>',
    role: 'admin',
    supervisorId: 'bob_clerk_user_id_placeholder',
    supervisorName: 'Bob Wazneh',
    permissions: {
      canManageUsers: true,
      canViewReports: true,
      canExportData: true,
      canManageSettings: false,
      canViewAllAdmins: false,
      canEditAdmins: false
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'romy_clerk_user_id_placeholder',
    fullName: 'Romy H',
    email: '<EMAIL>',
    role: 'admin',
    supervisorId: 'bob_clerk_user_id_placeholder',
    supervisorName: 'Bob Wazneh',
    permissions: {
      canManageUsers: true,
      canViewReports: true,
      canExportData: true,
      canManageSettings: false,
      canViewAllAdmins: false,
      canEditAdmins: false
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
]

export async function getAdminProfile(adminId: string): Promise<AdminProfile | null> {
  // In production, this would query the database
  // For now, return mock data
  return mockAdmins.find(admin => admin.id === adminId) || null
}

export async function getAdminPermissions(adminId: string): Promise<AdminPermissions> {
  const admin = await getAdminProfile(adminId)
  
  if (!admin) {
    return {
      canManageUsers: false,
      canViewReports: false,
      canExportData: false,
      canManageSettings: false,
      canViewAllAdmins: false,
      canEditAdmins: false
    }
  }
  
  return admin.permissions as AdminPermissions
}

export async function getAllAdmins(currentUserId?: string, currentUserRole?: string): Promise<AdminProfile[]> {
  if (!currentUserId || !currentUserRole) return []
  
  // Super-admin can see all admins
  if (currentUserRole === 'super-admin') {
    return mockAdmins
  }
  
  // Regular admins can only see themselves
  return mockAdmins.filter(admin => admin.id === currentUserId)
}

export async function canAccessAdminPage(adminId: string, currentUserId?: string, currentUserRole?: string): Promise<boolean> {
  if (!currentUserId || !currentUserRole) return false
  
  // Super-admin can access all admin pages
  if (currentUserRole === 'super-admin') {
    return true
  }
  
  // Regular admins can only access their own page
  return currentUserId === adminId
}

export async function getAdminStats(adminId: string, currentUserId?: string, currentUserRole?: string) {
  if (!currentUserId || !currentUserRole || !await canAccessAdminPage(adminId, currentUserId, currentUserRole)) {
    return null
  }
  
  // Mock stats - in production, query actual database
  return {
    totalEmployees: 15,
    activeAppraisals: 8,
    completedAppraisals: 12,
    pendingAppraisals: 3,
    departmentCount: 4,
    managerCount: 5,
    monthlyGrowth: 5.2,
    approvalRate: 85.7
  }
}

export async function updateAdminProfile(adminId: string, updates: Partial<AdminProfile>, currentUserRole?: string): Promise<AdminProfile | null> {
  if (!currentUserRole || currentUserRole !== 'super-admin') {
    throw new Error('Insufficient permissions')
  }
  
  // In production, update database
  const admin = mockAdmins.find(a => a.id === adminId)
  if (!admin) return null
  
  Object.assign(admin, updates, { updatedAt: new Date().toISOString() })
  return admin
}

export async function getAdminByEmail(email: string): Promise<AdminProfile | null> {
  return mockAdmins.find(admin => admin.email === email) || null
}